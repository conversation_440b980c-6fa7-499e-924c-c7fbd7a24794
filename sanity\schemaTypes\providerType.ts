import { CONTACT_TYPES } from "@/app/(site)/experience/schema";
import { Building2 } from "lucide-react";
import { defineField, defineType } from "sanity";
import { validatedArrayStringOrTextLength } from "./utils";

export const providerType = defineType({
  name: "provider",
  title: "Fornecedores",
  type: "document",
  icon: Building2,
  fields: [
    defineField({
      name: "name",
      title: "Nome",
      type: "internationalizedArrayString",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          minLength: 2,
          maxLength: 100,
          fieldName: "Nome do fornecedor",
        }),
    }),
    defineField({
      name: "description",
      title: "Descrição",
      type: "internationalizedArrayText",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          minLength: 10,
          maxLength: 1000,
          fieldName: "Descrição do fornecedor",
        }),
    }),
    defineField({
      name: "contacts",
      title: "Contatos",
      type: "array",
      of: [
        {
          type: "object",
          title: "Contato",
          fields: [
            {
              name: "type",
              title: "Tipo",
              type: "string",
              options: {
                list: Object.values(CONTACT_TYPES).map((value) => ({
                  title: value.label,
                  value: value.value,
                })),
                layout: "dropdown",
              },
              validation: (rule) =>
                rule.required().error("Tipo de contato é obrigatório"),
            },
            {
              name: "value",
              title: "Valor",
              type: "string",
              validation: (rule) => [
                rule
                  .min(2)
                  .error("Valor do contato deve ter pelo menos 2 caracteres"),
                rule
                  .max(500)
                  .error("Valor do contato deve ter no máximo 500 caracteres"),
              ],
            },
          ],
          preview: {
            select: {
              type: "type",
              value: "value",
            },
            prepare({ type, value }) {
              const typeLabel =
                CONTACT_TYPES[type as keyof typeof CONTACT_TYPES].label;
              return {
                title: `${typeLabel}: ${value}`,
              };
            },
          },
        },
      ],
      validation: (rule) => [
        rule
          .min(1)
          .error("Pelo menos um contato é obrigatório")
          .unique()
          .error("Contatos duplicados não são permitidos"),
      ],
    }),
  ],
  preview: {
    select: {
      title: "name.[0].value",
      subtitle: "description.[0].value",
    },
    prepare({ title, subtitle }) {
      return {
        title: title || "Fornecedor sem nome",
        subtitle: subtitle
          ? `${subtitle.substring(0, 60)}...`
          : "Sem descrição",
      };
    },
  },
});
