import { MessageCircle } from "lucide-react";
import { defineField, defineType } from "sanity";

export const commentType = defineType({
  name: "comment",
  title: "Comentários",
  type: "document",
  icon: MessageCircle,
  fields: [
    defineField({
      name: "userId",
      title: "ID do Usuário",
      type: "string",
      readOnly: true,
      validation: (rule) =>
        rule.required().error("ID do usuário é obrigatório"),
    }),
    defineField({
      name: "rating",
      title: "Avaliação",
      type: "number",
      readOnly: true,
      validation: (rule) => [
        rule.min(1).error("Avaliação mínima é 1"),
        rule.max(5).error("Avaliação máxima é 5"),
        rule.required().error("Avaliação é obrigatória"),
      ],
    }),
    defineField({
      name: "message",
      title: "Mensagem",
      type: "text",
      readOnly: true,
      validation: (rule) => [
        rule.min(2).error("Mensagem deve ter pelo menos 2 caracteres"),
        rule.max(500).error("Mensagem deve ter no máximo 1000 caracteres"),
      ],
    }),
    defineField({
      name: "experience",
      title: "Experiência",
      type: "reference",
      readOnly: true,
      to: [{ type: "experience" }],
      validation: (rule) =>
        rule.required().error("Referência à experiência é obrigatória"),
    }),
    defineField({
      name: "complaints",
      title: "Denúncias",
      type: "array",
      of: [{ type: "reference", to: [{ type: "complaint" }] }],
      readOnly: true,
    }),
  ],
  preview: {
    select: {
      rating: "rating",
      message: "message",
      userId: "userId",
      experienceName: "experience.name.[0].value",
    },
    prepare({ rating, message, userId, experienceName }) {
      const stars = "★".repeat(rating) + "☆".repeat(5 - rating);
      return {
        title: `${stars} - ${userId}`,
        subtitle: `${experienceName || "Experiência"}: ${message ? message.substring(0, 50) + "..." : "Sem mensagem"}`,
      };
    },
  },
});
