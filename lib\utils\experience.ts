import {
  CONTACT_TYPES,
  EXPERIENCE_TYPES,
  ExperienceType,
  ProviderType,
} from "@/app/(site)/experience/schema";
import { getProvinceLabel } from "@/lib/constants/angola-provinces";

// Experience formatting utilities
export const getFormattedExperienceLocation = (
  experience: ExperienceType,
): string => {
  if (!experience.locations || experience.locations.length === 0) {
    return "Local não informado";
  }

  const primaryLocation = experience.locations[0];
  const locationName = primaryLocation.name;
  const provinceName = getProvinceLabel(primaryLocation.province);

  return `${locationName}, ${provinceName}`;
};

export const getFormattedExperienceLocations = (
  experience: ExperienceType,
): string[] => {
  if (!experience.locations || experience.locations.length === 0) {
    return ["Local não informado"];
  }

  return experience.locations.map((location) => {
    const locationName = location.name;
    const provinceName = getProvinceLabel(location.province);
    return `${locationName}, ${provinceName}`;
  });
};

export const getExperienceTypeDisplayName = (type: string): string => {
  return EXPERIENCE_TYPES[type as keyof typeof EXPERIENCE_TYPES].label;
};

export const getFormattedExperiencePrice = (
  experience: ExperienceType,
): string => {
  if (!experience.prices || experience.prices.length === 0) {
    return "Preço não informado";
  }

  const minPrice = Math.min(...experience.prices.map((p) => p.price));
  const maxPrice = Math.max(...experience.prices.map((p) => p.price));

  if (minPrice === maxPrice) {
    return `${minPrice.toLocaleString("pt-AO")} Kz`;
  }

  return `${minPrice.toLocaleString("pt-AO")} - ${maxPrice.toLocaleString("pt-AO")} Kz`;
};

export const getExperiencePriceRange = (
  experience: ExperienceType,
): { min: number; max: number } | null => {
  if (!experience.prices || experience.prices.length === 0) {
    return null;
  }

  const prices = experience.prices.map((p) => p.price);
  return {
    min: Math.min(...prices),
    max: Math.max(...prices),
  };
};

export const isExperienceHighlighted = (
  experience: ExperienceType,
): boolean => {
  if (!experience.highlightedUntil) return false;
  return new Date(experience.highlightedUntil) >= new Date();
};

export const isExperienceSponsored = (experience: ExperienceType): boolean => {
  if (!experience.sponsoredUntil) return false;
  return new Date(experience.sponsoredUntil) >= new Date();
};

export const getFormattedExperienceDuration = (
  duration?: string | null,
): string => {
  if (!duration) return "Duração não informada";

  const [hours, minutes] = duration.split(":");
  const h = parseInt(hours, 10);
  const m = parseInt(minutes, 10);

  if (h === 0 && m === 0) return "Duração não informada";
  if (h === 0) return `${m} minutos`;
  if (m === 0) return `${h} ${h === 1 ? "hora" : "horas"}`;

  return `${h}h${m.toString().padStart(2, "0")}`;
};

export const getExperienceAvailabilityStatus = (
  experience: ExperienceType,
): "available" | "unavailable" | "unknown" => {
  if (!experience.availability || experience.availability.length === 0) {
    return "unknown";
  }

  const hasAvailable = experience.availability.some(
    (slot) => slot.status === "AVAILABLE",
  );
  return hasAvailable ? "available" : "unavailable";
};

export const getFormattedAvailabilityTime = (
  startAt: string,
  endAt?: string | null,
): string => {
  if (!endAt) return startAt;
  return `${startAt} - ${endAt}`;
};

// Provider formatting utilities
export const getFormattedProviderContacts = (
  provider: ProviderType,
): string[] => {
  if (!provider.contacts || provider.contacts.length === 0) {
    return [];
  }

  return provider.contacts.map((contact) => {
    const typeLabel =
      CONTACT_TYPES[contact.type as keyof typeof CONTACT_TYPES].label;
    return `${typeLabel}: ${contact.value}`;
  });
};

export const getProviderContactByType = (
  provider: ProviderType,
  type: string,
) => {
  if (!provider.contacts) return null;
  return provider.contacts.find((contact) => contact.type === type) || null;
};

// Search and filter utilities
export const filterExperiencesByType = (
  experiences: ExperienceType[],
  type: string,
): ExperienceType[] => {
  return experiences.filter((experience) => experience.type === type);
};

export const filterExperiencesByProvince = (
  experiences: ExperienceType[],
  province: string,
): ExperienceType[] => {
  return experiences.filter((experience) =>
    experience.locations?.some((location) => location.province === province),
  );
};

export const filterExperiencesByPriceRange = (
  experiences: ExperienceType[],
  minPrice?: number,
  maxPrice?: number,
): ExperienceType[] => {
  return experiences.filter((experience) => {
    if (!experience.prices || experience.prices.length === 0) return false;

    const experiencePrices = experience.prices.map((p) => p.price);
    const experienceMinPrice = Math.min(...experiencePrices);
    const experienceMaxPrice = Math.max(...experiencePrices);

    if (minPrice !== undefined && experienceMaxPrice < minPrice) return false;
    if (maxPrice !== undefined && experienceMinPrice > maxPrice) return false;

    return true;
  });
};

export const searchExperiences = (
  experiences: ExperienceType[],
  searchTerm: string,
): ExperienceType[] => {
  const term = searchTerm.toLowerCase().trim();
  if (!term) return experiences;

  return experiences.filter((experience) => {
    const name = experience.name.toLowerCase();
    const description = experience.description.toLowerCase();
    const providerName = experience.provider.name.toLowerCase();
    const locations =
      experience.locations?.map((l) => l.name.toLowerCase()).join(" ") || "";

    return (
      name.includes(term) ||
      description.includes(term) ||
      providerName.includes(term) ||
      locations.includes(term)
    );
  });
};

// Sorting utilities
export const sortExperiencesByName = (
  experiences: ExperienceType[],
  ascending = true,
): ExperienceType[] => {
  return [...experiences].sort((a, b) => {
    const comparison = a.name.localeCompare(b.name, "pt-AO");
    return ascending ? comparison : -comparison;
  });
};

export const sortExperiencesByPrice = (
  experiences: ExperienceType[],
  ascending = true,
): ExperienceType[] => {
  return [...experiences].sort((a, b) => {
    const aMinPrice =
      a.prices && a.prices.length > 0
        ? Math.min(...a.prices.map((p) => p.price))
        : 0;
    const bMinPrice =
      b.prices && b.prices.length > 0
        ? Math.min(...b.prices.map((p) => p.price))
        : 0;

    return ascending ? aMinPrice - bMinPrice : bMinPrice - aMinPrice;
  });
};

export const sortExperiencesByDate = (
  experiences: ExperienceType[],
  ascending = true,
): ExperienceType[] => {
  return [...experiences].sort((a, b) => {
    const aDate = new Date(a._createdAt);
    const bDate = new Date(b._createdAt);

    return ascending
      ? aDate.getTime() - bDate.getTime()
      : bDate.getTime() - aDate.getTime();
  });
};
