# Experience Plan for Zimbora

## User story

In the Zimbora App will be create a `Experience Screen` that will show the user experiences like Lodging, Restaurant & Bar, and Activities (Boat tours, tours, skydiving, dance classes, etc.). The user will be able to filter the experiences by type (Lodging, Restaurant & Bar, Activities), price, location.

## Provider Base Schema

- name: string, min 2 characters, max 100 characters, (en, pt);
- description: string, min 10 characters, max 1000 characters, (en, pt);
- contacts: list {
  - type: string, must be one of the following: WHATSAPP, WEBSITE, EMAIL, PHONE;
  - value: string, min 2 characters, max 500 characters;
    }

## Experience Base Schema

- name: string, min 2 characters, max 100 characters, (en, pt);
- description: string, min 10 characters, max 1000 characters, (en, pt);
- provider: reference to Provider;
- medias: array, max 10 images/videos;
- type: list enum (lodging, restaurant & bar, activity, other);
- locations: list {
  - name: string, min 2 characters, max 100 characters, (en, pt);
  - province: enum (list of angolan provinces);
  - mapLink: (optional) string, must be a valid url;
    }
- features: list {
  - name: string, min 2 characters, max 100 characters, (en, pt);
  - description: (optional) string, min 2 characters, max 1000 characters, (en, pt);
    }
- details: list {
  - title: string, min 2 characters, max 100 characters, (en, pt);
  - description: (optional) string, min 2 characters, max 1000 characters, (en, pt);
    }
- prices: list {
  - type: enum (relative, absolute);
  - price: number;
  - description: (optional) string, min 10 characters, max 1000 characters, (en, pt);
    }
- checkoutMethods: list {
  - name: string, min 2 characters, max 50 characters, (en, pt);
  - type: string, must be one of the following: WHATSAPP, WEBSITE, EMAIL, PHONE;
  - value: string, min 2 characters, max 500 characters;
    }
- highlightedUntil: datetime (optional);
- sponsoredUntil: datetime (optional);
- duration: string (HH:mm) (optional);
- availability: list {
  - startAt: string (HH:mm AM/PM);
  - endAt: (optional) string (HH:mm AM/PM);
  - label: (optional) string, min 2 characters, max 50 characters, (en, pt);
  - status: enum (available, unavailable);
    }
- items: (optional) list {
  - name: string, min 2 characters, max 100 characters, (en, pt);
  - description: string, min 10 characters, max 1000 characters, (en, pt);
    }

## Complaint Base Schema

- userId: string (read only);
- type: enum (spam, fake, duplicate, inappropriate, other) (read only);
- reason: (optional) string, min 2 characters, max 1000 characters (read only);
- status: enum (new, pending, approved, rejected) default (new);
- comment: (optional) reference to Comment (read only);

## Comment Schema (read only)

- userId: string;
- rating: number, min 1, max 5;
- message: (optional) string, min 2 characters, max 500 characters;
- experience: (optional) reference to Experience;
- complaints: (optional) list of Complaints;

## ExperienceFavorite Schema (read only)

- userId: string;
- experienceId: string;
- experience: reference to Experience;
