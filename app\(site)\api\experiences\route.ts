import { NextRequest, NextResponse } from "next/server";
import { getAllExperiences } from "@/app/(site)/experience/controller";
import { withApi<PERSON><PERSON>Auth } from "@/lib/auth";
import { resolveLanguage } from "../i18n";

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const searchParams = Object.fromEntries(
      request.nextUrl.searchParams.entries(),
    );
    const { lang: _, ...filters } = searchParams;

    const experiences = await getAllExperiences(filters, lang);

    if (!experiences) {
      return NextResponse.json(
        { error: "Falha ao buscar experiências" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: experiences });
  } catch (error) {
    console.error("Experiences API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApi<PERSON>eyAuth(getHandler);
