/**
 * Migration script to convert event location field from string to object format
 *
 * This script:
 * 1. Queries all events with string location values
 * 2. Transforms them to the new object structure with Google Maps links
 * 3. Updates each event with the new location format
 *
 * Run with: npx tsx scripts/migrate-location-data.ts
 */

import { clientWithToken } from "@/sanity/lib/client";
import { DEFAULT_PROVINCE } from "../lib/constants/angola-provinces";

type EventWithStringLocation = {
  _id: string;
  _type: "event";
  location: string;
};

type EventWithObjectLocation = {
  _id: string;
  _type: "event";
  location: {
    name: string;
    province: string;
    mapLink: string | null;
  };
};

type LocationObject = {
  name: string;
  province: string;
  mapLink: string | null;
};

/**
 * Detect province from location string (basic heuristics)
 * You can enhance this logic based on your data patterns
 */
function detectProvinceFromLocation(locationString: string): string {
  const location = locationString.toLowerCase();

  // Common patterns to detect provinces
  const provincePatterns = {
    luanda: [
      "luanda",
      "talatona",
      "kilamba",
      "marginal",
      "miramar",
      "ingombota",
    ],
    benguela: ["benguela", "lobito"],
    huambo: ["huambo"],
    lubango: ["lubango", "huíla", "huila"],
    cabinda: ["cabinda"],
    malanje: ["malanje"],
    uige: ["uíge", "uige"],
    bie: ["bié", "bie", "kuito"],
    moxico: ["moxico", "luena"],
    "cuando-cubango": ["cuando cubango", "menongue"],
    cunene: ["cunene", "ondjiva"],
    namibe: ["namibe", "moçâmedes"],
    zaire: ["zaire", "mbanza congo"],
    "lunda-norte": ["lunda norte", "dundo"],
    "lunda-sul": ["lunda sul", "saurimo"],
    bengo: ["bengo", "caxito"],
    "cuanza-norte": ["cuanza norte", "ndalatando"],
    "cuanza-sul": ["cuanza sul", "sumbe"],
  };

  // Check for province patterns in the location string
  for (const [province, patterns] of Object.entries(provincePatterns)) {
    if (patterns.some((pattern) => location.includes(pattern))) {
      return province;
    }
  }

  // Default to Luanda if no pattern matches
  return DEFAULT_PROVINCE;
}

/**
 * Generate Google Maps link for a location
 */
function generateGoogleMapsLink(locationName: string): string {
  // Encode the location name for URL
  const encodedLocation = encodeURIComponent(locationName.trim());
  return `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
}

/**
 * Transform string location to object format
 */
function transformLocationToObject(locationString: string): LocationObject {
  const locationName = locationString.trim();

  return {
    name: locationName,
    province: detectProvinceFromLocation(locationString),
    mapLink: generateGoogleMapsLink(locationName),
  };
}

/**
 * Main migration function
 */
async function migrateLocationData() {
  console.log("🚀 Starting location data migration...");

  try {
    // Step 1: Query all events with string location values
    console.log("📋 Querying events with string locations...");

    const eventsWithStringLocations = await clientWithToken.fetch<
      EventWithStringLocation[]
    >(`
      *[_type == "event" && defined(location) && !defined(location.name)] {
        _id,
        _type,
        location
      }
    `);

    // Step 2: Query all events with object locations but missing/null mapLinks
    console.log("📋 Querying events with object locations missing mapLinks...");

    const eventsWithMissingMapLinks = await clientWithToken.fetch<
      EventWithObjectLocation[]
    >(`
      *[_type == "event" && defined(location.name) && (!defined(location.mapLink) || location.mapLink == null)] {
        _id,
        _type,
        location
      }
    `);

    const totalEvents =
      eventsWithStringLocations.length + eventsWithMissingMapLinks.length;

    console.log(
      `📊 Found ${eventsWithStringLocations.length} events with string locations to migrate`,
    );
    console.log(
      `📊 Found ${eventsWithMissingMapLinks.length} events with missing mapLinks to update`,
    );
    console.log(`📊 Total events to process: ${totalEvents}`);

    if (totalEvents === 0) {
      console.log("✅ No events need migration. All done!");
      return;
    }

    // Step 3: Process each event
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ id: string; error: string }>,
    };

    // Process events with string locations
    for (const event of eventsWithStringLocations) {
      try {
        console.log(`🔄 Migrating event ${event._id}: "${event.location}"`);

        // Transform the location
        const newLocation = transformLocationToObject(event.location);

        console.log(
          `   → New format: ${newLocation.name} (${newLocation.province}) with mapLink`,
        );

        // Update the document
        await clientWithToken
          .patch(event._id)
          .set({ location: newLocation })
          .commit();

        results.success++;
        console.log(`✅ Successfully migrated event ${event._id}`);
      } catch (error) {
        results.failed++;
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        results.errors.push({ id: event._id, error: errorMessage });
        console.error(`❌ Failed to migrate event ${event._id}:`, errorMessage);
      }
    }

    // Process events with object locations missing mapLinks
    for (const event of eventsWithMissingMapLinks) {
      try {
        console.log(
          `🔄 Updating mapLink for event ${event._id}: "${event.location.name}"`,
        );

        // Generate mapLink for existing location
        const mapLink = generateGoogleMapsLink(event.location.name);

        console.log(`   → Adding mapLink: ${mapLink}`);

        // Update only the mapLink field
        await clientWithToken
          .patch(event._id)
          .set({ "location.mapLink": mapLink })
          .commit();

        results.success++;
        console.log(`✅ Successfully updated mapLink for event ${event._id}`);
      } catch (error) {
        results.failed++;
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        results.errors.push({ id: event._id, error: errorMessage });
        console.error(
          `❌ Failed to update mapLink for event ${event._id}:`,
          errorMessage,
        );
      }
    }

    // Step 4: Report results
    console.log("\n📈 Migration Results:");
    console.log(`✅ Successfully migrated: ${results.success} events`);
    console.log(`❌ Failed migrations: ${results.failed} events`);

    if (results.errors.length > 0) {
      console.log("\n❌ Errors:");
      results.errors.forEach(({ id, error }) => {
        console.log(`   - ${id}: ${error}`);
      });
    }

    console.log("\n🎉 Migration completed!");
  } catch (error) {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  migrateLocationData()
    .then(() => {
      console.log("✨ Migration script finished successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Migration script failed:", error);
      process.exit(1);
    });
}

export {
  migrateLocationData,
  transformLocationToObject,
  detectProvinceFromLocation,
  generateGoogleMapsLink,
};
