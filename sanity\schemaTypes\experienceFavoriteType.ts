import { Heart } from "lucide-react";
import { defineField, defineType } from "sanity";

export const experienceFavoriteType = defineType({
  name: "experienceFavorite",
  title: "Experiências Favoritas",
  type: "document",
  icon: Heart,
  fields: [
    defineField({
      name: "userId",
      title: "ID do Usuário",
      type: "string",
      readOnly: true,
      validation: (rule) =>
        rule.required().error("ID do usuário é obrigatório"),
    }),
    defineField({
      name: "experienceId",
      title: "ID da Experiência",
      type: "string",
      readOnly: true,
      validation: (rule) =>
        rule.required().error("ID da experiência é obrigatório"),
    }),
    defineField({
      name: "experience",
      title: "Experiência",
      type: "reference",
      to: [{ type: "experience" }],
      readOnly: true,
      validation: (rule) =>
        rule.required().error("Referência à experiência é obrigatória"),
    }),
  ],
  preview: {
    select: {
      userId: "userId",
      experienceName: "experience.name.[0].value",
    },
    prepare({ userId, experienceName }) {
      return {
        title: `${userId} ♥ ${experienceName || "Experiência desconhecida"}`,
        subtitle: "Experiência Favorita",
      };
    },
  },
});
