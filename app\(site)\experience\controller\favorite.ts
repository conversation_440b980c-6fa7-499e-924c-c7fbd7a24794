import { client, clientWithToken } from "@/sanity/lib/client";
import { z } from "zod";
import { experienceSchema } from "../schema";
import {
  getUserExperienceFavoritesQuery,
  getExperienceFavoriteQuery,
} from "@/sanity/queries/experience";
import { LocaleType, DEFAULT_LANGUAGE } from "../../api/i18n";

const createFavoriteExperienceId = ({
  userId,
  experienceId,
}: {
  userId: string;
  experienceId: string;
}) => {
  return `${userId}_-_${experienceId}`;
};

export const toggleFavoriteExperience = async ({
  userId,
  experienceId,
}: {
  userId: string;
  experienceId: string;
}): Promise<void> => {
  const hasFavorited = await hasUserFavoritedExperience({ experienceId, userId });

  if (hasFavorited) {
    await clientWithToken.delete(hasFavorited._id).catch((err) => {
      console.error("Failed to remove experience favorite:", err);
      throw err;
    });
  } else {
    await clientWithToken
      .createIfNotExists({
        _id: createFavoriteExperienceId({ userId, experienceId }),
        _type: "experienceFavorite",
        userId,
        experienceId,
        experience: {
          _type: "reference",
          _ref: experienceId,
        },
      })
      .catch((err) => {
        console.error("Failed to add experience favorite:", err);
        throw err;
      });
  }
};

export const getUserFavoriteExperiences = async (
  userId: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const experiences = await client
    .fetch<
      { experience: any }[]
    >(getUserExperienceFavoritesQuery, { userId, lang }, { cacheMode: "noStale" })
    .catch((err) => {
      console.error(err);
      return null;
    });

  if (!experiences) {
    return null;
  }

  const schema = z.array(experienceSchema).safeParse(experiences);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const hasUserFavoritedExperience = async ({
  experienceId,
  userId,
}: {
  experienceId: string;
  userId: string;
}) => {
  const result = await client
    .fetch<{
      _id: string;
    }>(getExperienceFavoriteQuery, { experienceId, userId }, { cacheMode: "noStale" })
    .catch((err) => {
      console.error(err);
      return null;
    });

  return result;
};
