import Image from "next/image";
import Link from "next/link";
import React from "react";

const footerLinks = [
  {
    name: "Politica de Privacidade",
    href: "/privacy-policy",
  },
  {
    name: "Termos de Uso",
    href: "/terms-of-use",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/contact",
  },
  {
    name: "Política de cookies",
    href: "/cookie-policy",
  },
];

const Footer = () => {
  return (
    <footer className="from-dark-primary to-dark-secondary m-6 rounded-t-lg bg-gradient-to-l p-4 shadow md:m-8 md:px-6 md:py-8">
      <div className="grid gap-4 sm:grid-flow-col sm:items-center sm:justify-between">
        <Link
          href="/"
          className="mb-4 flex cursor-pointer items-center sm:mb-0"
        >
          <Image
            height={48}
            width={192}
            src="/icons/logo_icon_h.svg"
            className="mr-3 h-12"
            alt="Zimbora Logo"
          />
        </Link>
        <ul className="mb-6 flex flex-wrap items-center space-x-4 text-sm sm:mb-0">
          {footerLinks.map((link) => (
            <li
              key={link.name}
              className="hover:text-primary2 text-white duration-150 hover:underline"
            >
              <Link className="text-[inherit]" href={link.href}>
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      <hr className="border-tertiary2/40 my-6 sm:mx-auto lg:my-8" />
      <span className="text-light-primary block text-sm sm:text-center">
        © 2025{" "}
        <a
          className="text-[inherit] hover:underline"
          href="https://zimbora.ao/"
        >
          Zimbora™
        </a>
        . All Rights Reserved.
      </span>
    </footer>
  );
};

export default Footer;
