import {
  COMPLAINT_TYPES,
  COMPLAINT_STATUS,
} from "@/app/(site)/experience/schema";
import { AlertTriangle } from "lucide-react";
import { defineField, defineType } from "sanity";

export const complaintType = defineType({
  name: "complaint",
  title: "<PERSON><PERSON><PERSON><PERSON>",
  type: "document",
  icon: AlertTriangle,
  fields: [
    defineField({
      name: "userId",
      title: "ID do Usuário",
      type: "string",
      readOnly: true,
      validation: (rule) =>
        rule.required().error("ID do usuário é obrigatório"),
    }),
    defineField({
      name: "type",
      title: "Tipo",
      type: "string",
      options: {
        list: Object.values(COMPLAINT_TYPES).map((value) => ({
          title: value.label,
          value: value.value,
        })),
        layout: "dropdown",
      },
      readOnly: true,
      validation: (rule) =>
        rule.required().error("Tipo da denúncia é obrigatório"),
    }),
    defineField({
      name: "reason",
      title: "Motivo",
      type: "text",
      readOnly: true,
      validation: (rule) => [
        rule.min(2).error("Motivo deve ter pelo menos 2 caracteres"),
        rule.max(1000).error("Motivo deve ter no máximo 1000 caracteres"),
      ],
    }),
    defineField({
      name: "status",
      title: "Status",
      type: "string",
      options: {
        list: Object.values(COMPLAINT_STATUS).map((value) => ({
          title: value.label,
          value: value.value,
        })),
        layout: "dropdown",
      },
      initialValue: COMPLAINT_STATUS.NEW.value,
      validation: (rule) => rule.required().error("Status é obrigatório"),
    }),
    defineField({
      name: "comment",
      title: "Comentário",
      type: "reference",
      to: [{ type: "comment" }],
      readOnly: true,
      description: "Comentário relacionado à denúncia (se aplicável)",
    }),
  ],
  preview: {
    select: {
      type: "type",
      status: "status",
      userId: "userId",
      reason: "reason",
    },
    prepare({ type, status, userId, reason }) {
      const typeLabel =
        COMPLAINT_TYPES[type as keyof typeof COMPLAINT_TYPES].label;

      const statusLabel =
        COMPLAINT_STATUS[status as keyof typeof COMPLAINT_STATUS].label;

      return {
        title: `${typeLabel} - ${userId}`,
        subtitle: `${statusLabel}: ${reason ? reason.substring(0, 50) + "..." : "Sem motivo"}`,
      };
    },
  },
});
