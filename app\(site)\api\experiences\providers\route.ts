import { NextRequest, NextResponse } from "next/server";
import { withApi<PERSON>eyAuth } from "@/lib/auth";
import { resolveLanguage } from "../../i18n";
import { getExperienceProviders } from "@/app/(site)/experience/controller";

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const providers = await getExperienceProviders(lang);

    if (!providers) {
      return NextResponse.json(
        { error: "Falha ao buscar fornecedores de experiências" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: providers });
  } catch (error) {
    console.error("Experience Providers API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
