import { getExperienceById } from "@/app/(site)/experience/controller";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ApiHandlerProps, withApiKeyAuth } from "@/lib/auth";
import { resolveLanguage } from "../../i18n";

const getSchema = z.object({
  id: z.string(),
});

const getHandler: ApiHandlerProps<{ id: string }> = async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) => {
  try {
    const { success, data } = await getSchema.safeParse(await params);
    if (!success) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }

    const lang = resolveLanguage({ headers: request.headers });

    const experience = await getExperienceById(data.id, lang);

    if (!experience) {
      return NextResponse.json(
        { error: "Experiência não encontrada" },
        { status: 404 },
      );
    }

    return NextResponse.json({ data: experience });
  } catch (error) {
    console.error("Experience ID API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
};

export const GET = withApiKeyAuth(getHandler);
