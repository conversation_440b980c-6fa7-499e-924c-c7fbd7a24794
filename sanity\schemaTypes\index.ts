import { type SchemaTypeDefinition } from "sanity";

import { blockContentType } from "./blockContentType";
import { categoryType } from "./categoryType";
import { eventType } from "./eventType";
// import { postType } from "./postType";
// import { authorType } from "./authorType";
import { favoriteEventType } from "./favoriteEventType";
import { notificationType } from "./notificationType";
import { providerType } from "./providerType";
import { experienceType } from "./experienceType";
import { commentType } from "./commentType";
import { complaintType } from "./complaintType";
import { experienceFavoriteType } from "./experienceFavoriteType";

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    categoryType,
    eventType,
    favoriteEventType,
    notificationType,
    providerType,
    experienceType,
    commentType,
    complaintType,
    experienceFavoriteType,
  ],
};
