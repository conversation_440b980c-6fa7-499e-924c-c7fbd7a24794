import { NextRequest, NextResponse } from "next/server";
import { withApi<PERSON>eyAuth } from "@/lib/auth";
import { resolveLanguage } from "../../i18n";
import { getExperienceLocations } from "@/app/(site)/experience/controller";

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const locations = await getExperienceLocations(lang);

    if (!locations) {
      return NextResponse.json(
        { error: "Falha ao buscar localizações de experiências" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: locations });
  } catch (error) {
    console.error("Experience Locations API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
