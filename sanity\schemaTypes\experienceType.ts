import {
  EXPERIENCE_TYPES,
  CONTACT_TYPES,
  PRICE_TYPES,
  AVAILABILITY_STATUS,
} from "@/app/(site)/experience/schema";
import {
  ANGOLA_PROVINCES,
  DEFAULT_PROVINCE,
} from "@/lib/constants/angola-provinces";
import { MapPin } from "lucide-react";
import { defineField, defineType } from "sanity";
import { validatedArrayStringOrTextLength } from "./utils";

export const experienceType = defineType({
  name: "experience",
  title: "Experiências",
  type: "document",
  icon: MapPin,
  fields: [
    defineField({
      name: "type",
      title: "Tipo de Experiência",
      type: "string",
      options: {
        list: Object.values(EXPERIENCE_TYPES).map((value) => ({
          title: value.label,
          value: value.value,
        })),
        layout: "dropdown",
      },
      validation: (rule) =>
        rule.required().error("Tipo da experiência é obrigatório"),
    }),
    defineField({
      name: "name",
      title: "Nome",
      type: "internationalizedArrayString",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          minLength: 2,
          maxLength: 100,
          fieldName: "Nome da experiência",
        }),
    }),
    defineField({
      name: "description",
      title: "Descrição",
      type: "internationalizedArrayText",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          minLength: 10,
          maxLength: 1000,
          fieldName: "Descrição da experiência",
        }),
    }),
    defineField({
      name: "provider",
      title: "Fornecedor",
      type: "reference",
      to: [{ type: "provider" }],
      validation: (rule) => rule.required().error("Fornecedor é obrigatório"),
    }),
    defineField({
      name: "medias",
      title: "Mídias",
      type: "array",
      of: [
        {
          type: "image",
          options: {
            hotspot: true,
          },
        },
      ],
      validation: (rule) =>
        rule.max(10).error("Máximo de 10 imagens permitidas"),
    }),
    defineField({
      name: "locations",
      title: "Localizações",
      type: "array",
      of: [
        {
          type: "object",
          title: "Localização",
          fields: [
            {
              name: "name",
              title: "Nome do Local",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 100,
                  fieldName: "Nome do local",
                }),
            },
            {
              name: "province",
              title: "Província",
              type: "string",
              options: {
                list: Object.entries(ANGOLA_PROVINCES).map(([key, value]) => ({
                  title: value,
                  value: key,
                })),
                layout: "dropdown",
              },
              initialValue: DEFAULT_PROVINCE,
              validation: (rule) =>
                rule.required().error("Província é obrigatória"),
            },
            {
              name: "mapLink",
              title: "Link do Mapa",
              type: "url",
              description: "Link para o Google Maps ou similar",
            },
          ],
          preview: {
            select: {
              name: "name.[0].value",
              province: "province",
            },
            prepare({ name, province }) {
              const provinceName =
                ANGOLA_PROVINCES[province as keyof typeof ANGOLA_PROVINCES] ||
                province;
              return {
                title: name || "Local sem nome",
                subtitle: provinceName,
              };
            },
          },
        },
      ],
      validation: (rule) =>
        rule.min(1).error("Pelo menos uma localização é obrigatória"),
    }),
    defineField({
      name: "features",
      title: "Características",
      type: "array",
      of: [
        {
          type: "object",
          title: "Característica",
          fields: [
            {
              name: "name",
              title: "Nome",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 100,
                  fieldName: "Nome da característica",
                }),
            },
            {
              name: "description",
              title: "Descrição",
              type: "internationalizedArrayText",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 1000,
                  fieldName: "Descrição da característica",
                }),
            },
          ],
          preview: {
            select: {
              title: "name.[0].value",
              subtitle: "description.[0].value",
            },
            prepare({ title, subtitle }) {
              return {
                title: title || "Característica sem nome",
                subtitle: subtitle
                  ? `${subtitle.substring(0, 50)}...`
                  : "Sem descrição",
              };
            },
          },
        },
      ],
    }),
    defineField({
      name: "details",
      title: "Detalhes",
      type: "array",
      of: [
        {
          type: "object",
          title: "Detalhe",
          fields: [
            {
              name: "title",
              title: "Título",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 100,
                  fieldName: "Título do detalhe",
                }),
            },
            {
              name: "description",
              title: "Descrição",
              type: "internationalizedArrayText",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 1000,
                  fieldName: "Descrição do detalhe",
                }),
            },
          ],
          preview: {
            select: {
              title: "title.[0].value",
              subtitle: "description.[0].value",
            },
            prepare({ title, subtitle }) {
              return {
                title: title || "Detalhe sem título",
                subtitle: subtitle
                  ? `${subtitle.substring(0, 50)}...`
                  : "Sem descrição",
              };
            },
          },
        },
      ],
    }),
    defineField({
      name: "prices",
      title: "Preços",
      type: "array",
      of: [
        {
          type: "object",
          title: "Preço",
          fields: [
            {
              name: "type",
              title: "Tipo",
              type: "string",
              options: {
                list: Object.values(PRICE_TYPES).map((value) => ({
                  title: value.label,
                  value: value.value,
                })),
                layout: "dropdown",
              },
              validation: (rule) =>
                rule.required().error("Tipo de preço é obrigatório"),
            },
            {
              name: "price",
              title: "Preço",
              type: "number",
              validation: (rule) => [
                rule.min(0).error("Preço deve ser maior ou igual a 0"),
              ],
            },
            {
              name: "description",
              title: "Descrição",
              type: "internationalizedArrayText",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 10,
                  maxLength: 1000,
                  fieldName: "Descrição do preço",
                }),
            },
          ],
          preview: {
            select: {
              type: "type",
              price: "price",
              description: "description.[0].value",
            },
            prepare({ type, price, description }) {
              const typeLabel =
                PRICE_TYPES[type as keyof typeof PRICE_TYPES].label;
              return {
                title: `${typeLabel}: ${price}`,
                subtitle: description
                  ? `${description.substring(0, 50)}...`
                  : "Sem descrição",
              };
            },
          },
        },
      ],
    }),
    defineField({
      name: "checkoutMethods",
      title: "Métodos de Checkout",
      type: "array",
      of: [
        {
          type: "object",
          title: "Método de Checkout",
          fields: [
            {
              name: "name",
              title: "Nome",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 50,
                  fieldName: "Nome do método de checkout",
                }),
            },
            {
              name: "type",
              title: "Tipo",
              type: "string",
              options: {
                list: Object.values(CONTACT_TYPES).map((value) => ({
                  title: value.label,
                  value: value.value,
                })),
                layout: "dropdown",
              },
              validation: (rule) =>
                rule.required().error("Tipo de checkout é obrigatório"),
            },
            {
              name: "value",
              title: "Valor",
              type: "string",
              validation: (rule) => [
                rule.min(2).error("Valor deve ter pelo menos 2 caracteres"),
                rule.max(500).error("Valor deve ter no máximo 500 caracteres"),
              ],
            },
          ],
          preview: {
            select: {
              name: "name.[0].value",
              type: "type",
              value: "value",
            },
            prepare({ name, type, value }) {
              const typeLabel =
                CONTACT_TYPES[type as keyof typeof CONTACT_TYPES].label;
              return {
                title: name || "Método sem nome",
                subtitle: `${typeLabel}: ${value}`,
              };
            },
          },
        },
      ],
    }),
    defineField({
      name: "highlightedUntil",
      title: "Destacado até",
      type: "datetime",
      description: "Data até quando a experiência ficará destacada",
    }),
    defineField({
      name: "sponsoredUntil",
      title: "Patrocinado até",
      type: "datetime",
      description: "Data até quando a experiência ficará patrocinada",
    }),
    defineField({
      name: "duration",
      title: "Duração",
      type: "string",
      description: "Duração da experiência (formato HH:mm)",
      validation: (rule) =>
        rule
          .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
            name: "time format",
            invert: false,
          })
          .warning("Formato deve ser HH:mm (ex: 02:30)"),
    }),
    defineField({
      name: "availability",
      title: "Disponibilidade",
      type: "array",
      of: [
        {
          type: "object",
          title: "Horário",
          fields: [
            {
              name: "startAt",
              title: "Início",
              type: "string",
              description: "Horário de início (formato HH:mm AM/PM)",
              validation: (rule) => [
                rule.required().error("Horário de início é obrigatório"),
                rule
                  .regex(/^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i, {
                    name: "time format",
                    invert: false,
                  })
                  .warning("Formato deve ser HH:mm AM/PM (ex: 09:00 AM)"),
              ],
            },
            {
              name: "endAt",
              title: "Fim",
              type: "string",
              description: "Horário de fim (formato HH:mm AM/PM) - opcional",
              validation: (rule) =>
                rule
                  .regex(/^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i, {
                    name: "time format",
                    invert: false,
                  })
                  .warning("Formato deve ser HH:mm AM/PM (ex: 05:00 PM)"),
            },
            {
              name: "label",
              title: "Rótulo",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 50,
                  fieldName: "Rótulo do horário",
                }),
            },
            {
              name: "status",
              title: "Status",
              type: "string",
              options: {
                list: Object.values(AVAILABILITY_STATUS).map((value) => ({
                  title: value.label,
                  value: value.value,
                })),
                layout: "dropdown",
              },
              initialValue: AVAILABILITY_STATUS.AVAILABLE.value,
              validation: (rule) =>
                rule.required().error("Status é obrigatório"),
            },
          ],
          preview: {
            select: {
              startAt: "startAt",
              endAt: "endAt",
              status: "status",
              label: "label.[0].value",
            },
            prepare({ startAt, endAt, status, label }) {
              const statusLabel =
                AVAILABILITY_STATUS[status as keyof typeof AVAILABILITY_STATUS]
                  .label;
              const timeRange = endAt ? `${startAt} - ${endAt}` : startAt;
              return {
                title: label || timeRange,
                subtitle: `${timeRange} (${statusLabel})`,
              };
            },
          },
        },
      ],
    }),
    defineField({
      name: "items",
      title: "Itens",
      type: "array",
      of: [
        {
          type: "object",
          title: "Item",
          fields: [
            {
              name: "name",
              title: "Nome",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 2,
                  maxLength: 100,
                  fieldName: "Nome do item",
                }),
            },
            {
              name: "description",
              title: "Descrição",
              type: "internationalizedArrayText",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 10,
                  maxLength: 1000,
                  fieldName: "Descrição do item",
                }),
            },
          ],
          preview: {
            select: {
              title: "name.[0].value",
              subtitle: "description.[0].value",
            },
            prepare({ title, subtitle }) {
              return {
                title: title || "Item sem nome",
                subtitle: subtitle
                  ? `${subtitle.substring(0, 50)}...`
                  : "Sem descrição",
              };
            },
          },
        },
      ],
    }),
  ],
  preview: {
    select: {
      title: "name.[0].value",
      subtitle: "type",
      media: "medias.0",
    },
    prepare({ title, subtitle }) {
      const typeLabel =
        subtitle === "lodging"
          ? "Hospedagem"
          : subtitle === "restaurant-bar"
            ? "Restaurante & Bar"
            : subtitle === "activity"
              ? "Atividade"
              : "Outro";
      return {
        title: title || "Experiência sem nome",
        subtitle: typeLabel,
      };
    },
  },
});
