import { EVENT_CHECKOUT_METHODS_TYPES } from "@/app/(site)/event/schema";
import {
  ANGOLA_PROVINCES,
  DEFAULT_PROVINCE,
} from "@/lib/constants/angola-provinces";
import { Calendar } from "lucide-react";
import { defineField, defineType } from "sanity";

import {
  validatedArrayStringOrTextLength,
  validateActivityTimes,
} from "./utils";

export const eventType = defineType({
  name: "event",
  title: "Eventos",
  type: "document",
  icon: Calendar,
  fields: [
    defineField({
      name: "name",
      title: "Nome",
      type: "internationalizedArrayString",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          maxLength: 100,
          fieldName: "Nome do evento",
        }),
    }),
    defineField({
      name: "slug",
      type: "slug",
      options: {
        source: "name",
      },
    }),
    defineField({
      name: "organizer",
      title: "Organizador",
      type: "string",
      validation: (rule) =>
        rule
          .max(50)
          .warning("Nome do organizador deve ter no máximo 50 caracteres")
          .required()
          .error("Organizador é obrigatório"),
    }),
    defineField({
      name: "description",
      title: "Descrição",
      type: "internationalizedArrayText",
      validation: (rule) =>
        validatedArrayStringOrTextLength(rule, {
          maxLength: 1000,
          fieldName: "Descrição",
        }),
    }),
    defineField({
      name: "location",
      title: "Local",
      type: "object",
      fields: [
        {
          name: "name",
          title: "Nome do Local",
          type: "string",
          description:
            "Nome específico do local (ex: Centro de Convenções de Talatona)",
          validation: (rule) =>
            rule
              .max(100)
              .warning("Nome do local deve ter no máximo 100 caracteres")
              .required()
              .error("Nome do local é obrigatório"),
        },
        {
          name: "province",
          title: "Província",
          type: "string",
          options: {
            list: Object.entries(ANGOLA_PROVINCES).map(([key, value]) => ({
              title: value,
              value: key,
            })),
            layout: "dropdown",
          },
          initialValue: DEFAULT_PROVINCE,
          validation: (rule) =>
            rule.required().error("Província é obrigatória"),
        },
        {
          name: "mapLink",
          title: "Link do Google Maps",
          type: "url",
          description: "Link opcional para o Google Maps do local",
          validation: (rule) =>
            rule.uri({
              scheme: ["http", "https"],
            }),
        },
      ],
      validation: (rule) => rule.required().error("Local é obrigatório"),
    }),
    defineField({
      name: "startAt",
      title: "Data de Início",
      type: "datetime",
      validation: (rule) =>
        rule.required().error("Data de início é obrigatória"),
    }),
    defineField({
      name: "endAt",
      title: "Data de Fim",
      type: "datetime",
      validation: (rule) => rule.required().error("Data de fim é obrigatória"),
    }),
    defineField({
      name: "highlightedUntil",
      title: "Em destaque até",
      type: "datetime",
    }),
    defineField({
      name: "sponsoredUntil",
      title: "Patrocinado até",
      type: "datetime",
    }),
    defineField({
      name: "prices",
      title: "Preços",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              type: "string",
              validation: (rule) =>
                rule
                  .max(50)
                  .warning("Nome deve ter no máximo 50 caracteres")
                  .required()
                  .error("Nome é obrigatório"),
            },
            {
              name: "price",
              type: "number",
              validation: (rule) =>
                rule.required().error("Preço é obrigatório"),
            },
            {
              name: "description",
              type: "text",
              validation: (rule) =>
                rule
                  .max(1000)
                  .warning("Descrição deve ter no máximo 1000 caracteres"),
            },
          ],
        },
      ],
      validation: (rule) =>
        rule
          .min(1)
          .warning("Ao menos um preço deve ser definido")
          .required()
          .error("Ao menos um preço deve ser definido"),
    }),
    defineField({
      name: "categories",
      title: "Categorias",
      type: "array",
      of: [{ type: "reference", to: { type: "category" } }],
    }),
    defineField({
      name: "medias",
      title: "Imagens",
      type: "array",
      of: [
        {
          type: "image",
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: "alt",
              type: "string",
              validation: (rule) =>
                rule
                  .max(200)
                  .warning("Descrição deve ter no máximo 200 caracteres"),
            },
          ],
        },
      ],
    }),
    defineField({
      name: "activities",
      title: "Atividades",
      type: "array",
      of: [
        {
          type: "object",
          title: "Atividade",
          fields: [
            {
              name: "description",
              title: "Descrição",
              type: "internationalizedArrayText",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 5,
                  maxLength: 500,
                  fieldName: "Descrição da atividade",
                }),
            },
            {
              name: "startTime",
              title: "Hora de Início",
              type: "datetime",
              validation: (rule) =>
                rule.required().error("Hora de início é obrigatória"),
            },
            {
              name: "endTime",
              title: "Hora de Fim",
              type: "datetime",
              description:
                "Opcional - deixe em branco se a atividade não tem hora de fim definida",
            },
            {
              name: "location",
              title: "Local da Atividade",
              type: "internationalizedArrayString",
              validation: (rule) =>
                validatedArrayStringOrTextLength(rule, {
                  minLength: 5,
                  maxLength: 100,
                  fieldName: "Local da atividade",
                }),
            },
          ],
          preview: {
            select: {
              title: "description.[0].value",
              subtitle: "location.[0].value",
              startTime: "startTime",
              endTime: "endTime",
            },
            prepare({ title, subtitle, startTime, endTime }) {
              const start = startTime
                ? new Date(startTime).toLocaleTimeString("pt-BR", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : "";
              const end = endTime
                ? new Date(endTime).toLocaleTimeString("pt-BR", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : "";
              const timeRange = end ? `${start} - ${end}` : start;

              return {
                title: title || "Atividade sem descrição",
                subtitle: `${timeRange}${subtitle ? ` • ${subtitle}` : ""}`,
              };
            },
          },
        },
      ],
      validation: (rule) => validateActivityTimes(rule),
    }),
    defineField({
      name: "checkoutMethods",
      title: "Métodos de Checkout",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Nome",
              type: "string",
              validation: (rule) => [
                rule.required().error("Nome é obrigatório"),
                // rule.unique().error("Nome deve ser único"),
              ],
            },
            {
              name: "type",
              title: "Tipo",
              type: "string",
              options: {
                list: Object.entries(EVENT_CHECKOUT_METHODS_TYPES).map(
                  ([key, value]) => ({ title: value, value: key }),
                ),
                layout: "dropdown",
              },
              validation: (rule) => rule.required().error("Tipo é obrigatório"),
            },
            {
              name: "value",
              title: "Valor",
              type: "string",
              validation: (rule) =>
                rule.required().error("Valor é obrigatório"),
            },
          ],
        },
      ],
    }),
    defineField({
      name: "createdBy",
      title: "Criado por",
      type: "string",
      validation: (rule) =>
        rule
          .max(50)
          .warning("Nome do criador deve ter no máximo 50 caracteres")
          .required()
          .error("Nome do criador é obrigatório"),
    }),
  ],
  preview: {
    select: {
      title: "name.[0].value",
      subtitle: "organizer",
      media: "medias.0",
    },
  },
});
