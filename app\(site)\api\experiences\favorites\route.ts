import { NextRequest, NextResponse } from "next/server";
import { withA<PERSON><PERSON>eyAuth } from "@/lib/auth";
import { resolveLanguage } from "../../i18n";
import {
  getUserExperienceFavorites,
  getExperienceFavorite,
} from "@/app/(site)/experience/controller";
import { toggleFavoriteExperience } from "@/app/(site)/experience/controller/favorite";
import * as z from "zod";

const getFavoriteParamsSchema = z.object({
  userId: z.string(),
  experienceId: z.string().optional(),
  type: z.enum(["check-favorited", "get-user-favorites"]).optional(),
});

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const paramsSchema = await getFavoriteParamsSchema.safeParse(
      Object.fromEntries(request.nextUrl.searchParams.entries()),
    );
    if (!paramsSchema.success) {
      return NextResponse.json(
        { error: z.prettifyError(paramsSchema.error) },
        { status: 400 },
      );
    }
    const { userId, experienceId, type } = paramsSchema.data;

    if (type === "check-favorited") {
      if (!experienceId) {
        return NextResponse.json(
          { error: "ID da experiência é obrigatório para verificar favorito" },
          { status: 400 },
        );
      }

      const favorite = await getExperienceFavorite(userId, experienceId, lang);
      return NextResponse.json({ data: { isFavorited: !!favorite } });
    }

    if (type === "get-user-favorites" || !type) {
      const favorites = await getUserExperienceFavorites(userId, lang);

      if (!favorites) {
        return NextResponse.json(
          { error: "Falha ao buscar experiências favoritas" },
          { status: 500 },
        );
      }

      return NextResponse.json({ data: favorites });
    }

    return NextResponse.json(
      { error: "Tipo de operação inválido" },
      { status: 400 },
    );
  } catch (error) {
    console.error("Experience Favorites API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

const postFavoriteParamsSchema = z.object({
  userId: z.string(),
  experienceId: z.string(),
  type: z.enum(["toggle-favorite"]).optional(),
});

async function postHandler(request: NextRequest) {
  try {
    const paramsSchema = await postFavoriteParamsSchema.safeParse(
      await request.json(),
    );
    if (!paramsSchema.success) {
      return NextResponse.json(
        { error: z.prettifyError(paramsSchema.error) },
        { status: 400 },
      );
    }
    const { userId, experienceId, type } = paramsSchema.data;

    if (!userId || !experienceId) {
      return NextResponse.json(
        { error: "ID do usuário e ID da experiência são obrigatórios" },
        { status: 400 },
      );
    }

    if (type === "toggle-favorite") {
      await toggleFavoriteExperience({ userId, experienceId });

      return NextResponse.json({
        message: "Favorito alternado com sucesso",
        data: { success: true },
      });
    }

    return NextResponse.json(
      { error: "Tipo de operação inválido" },
      { status: 400 },
    );
  } catch (error) {
    console.error("Experience Favorites POST API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
export const POST = withApiKeyAuth(postHandler);
