/**
 * Dry-run version of the location migration script
 * 
 * This script shows what would be migrated without making actual changes
 * Run with: npx tsx scripts/migrate-location-data-dry-run.ts
 */

import { client } from '../sanity/lib/client';
import { transformLocationToObject, detectProvinceFromLocation } from './migrate-location-data';

type EventWithStringLocation = {
  _id: string;
  _type: 'event';
  location: string;
};

async function dryRunLocationMigration() {
  console.log('🔍 Running location migration dry-run...');
  
  try {
    // Query all events with string location values
    console.log('📋 Querying events with string locations...');
    
    const eventsWithStringLocations = await client.fetch<EventWithStringLocation[]>(`
      *[_type == "event" && defined(location) && !defined(location.name)] {
        _id,
        _type,
        location
      }
    `);

    console.log(`📊 Found ${eventsWithStringLocations.length} events that would be migrated`);

    if (eventsWithStringLocations.length === 0) {
      console.log('✅ No events need migration.');
      return;
    }

    console.log('\n📋 Migration Preview:');
    console.log('='.repeat(80));

    // Show what would be changed
    eventsWithStringLocations.forEach((event, index) => {
      const newLocation = transformLocationToObject(event.location);
      
      console.log(`\n${index + 1}. Event ID: ${event._id}`);
      console.log(`   Current: "${event.location}"`);
      console.log(`   New format:`);
      console.log(`     - name: "${newLocation.name}"`);
      console.log(`     - province: "${newLocation.province}"`);
      console.log(`     - mapLink: ${newLocation.mapLink || 'null'}`);
    });

    console.log('\n' + '='.repeat(80));
    console.log(`📊 Summary: ${eventsWithStringLocations.length} events would be migrated`);
    
    // Show province distribution
    const provinceCount = eventsWithStringLocations.reduce((acc, event) => {
      const province = detectProvinceFromLocation(event.location);
      acc[province] = (acc[province] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('\n🗺️  Province Distribution:');
    Object.entries(provinceCount)
      .sort(([,a], [,b]) => b - a)
      .forEach(([province, count]) => {
        console.log(`   ${province}: ${count} events`);
      });

    console.log('\n💡 To run the actual migration, use:');
    console.log('   pnpm migrate:location');
    
  } catch (error) {
    console.error('💥 Dry-run failed:', error);
    process.exit(1);
  }
}

// Run the dry-run
if (require.main === module) {
  dryRunLocationMigration()
    .then(() => {
      console.log('\n✨ Dry-run completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Dry-run failed:', error);
      process.exit(1);
    });
}

export { dryRunLocationMigration };
