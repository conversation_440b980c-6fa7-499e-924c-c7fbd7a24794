import { defineQuery } from "next-sanity";

const categoryField = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  slug,
  description,
  type,
  createdBy
`;

const fullCategoryFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  slug,
  description,
  type,
  events[]-> {
    _id,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    slug,
    startAt,
    endAt,
    "location": coalesce(location.name, location, "Local não informado"),
    "locationObj": {
      "name": coalesce(location.name, location, "Local não informado"),
      "province": coalesce(location.province, "luanda"),
      "mapLink": location.mapLink
    }
  },
  createdBy
`;

export const getAllCategoriesQuery = defineQuery(
  `*[_type == "category" && defined(slug.current)] {
    ${categoryField}
  }`,
);

export const getCategoryBySlugQuery = defineQuery(`
  *[_type == "category" && slug.current == $slug][0] {
    ${fullCategoryFields}
  }
`);
